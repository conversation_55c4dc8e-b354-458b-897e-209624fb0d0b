/* ===== المتغيرات العامة ===== */
:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;

    /* ألوان الخلفية */
    --bg-primary: #f0f2f5;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;

    /* ألوان النص */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    
    /* ألوان الحالة */
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #f56565;
    --info-color: #4299e1;
    
    /* الظلال */
    --shadow-light: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
    --shadow-inset: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
    --shadow-hover: 4px 4px 8px #d1d9e6, -4px -4px 8px #ffffff;
    
    /* الخطوط */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الحدود */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    
    /* الانتقالات */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* الثيم الداكن */
[data-theme="dark"] {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-card: #2d3748;
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --shadow-light: 8px 8px 16px #0f1419, -8px -8px 16px #252f3f;
    --shadow-inset: inset 8px 8px 16px #0f1419, inset -8px -8px 16px #252f3f;
    --shadow-hover: 4px 4px 8px #0f1419, -4px -4px 8px #252f3f;
}

/* ===== الإعدادات العامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* ===== الفئات المساعدة ===== */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

/* ===== شاشة تسجيل الدخول ===== */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.login-container {
    background: var(--bg-card);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header .logo {
    margin-bottom: var(--spacing-xl);
}

.login-header .logo i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.login-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.login-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

.login-form {
    margin-top: var(--spacing-xl);
}

.input-group {
    margin-bottom: var(--spacing-lg);
    text-align: right;
}

.input-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.input-wrapper {
    position: relative;
}

.input-wrapper input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 3rem;
    border: none;
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.input-wrapper input:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-wrapper i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.login-btn {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-family: var(--font-family);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.login-btn:active {
    transform: translateY(0);
}

.login-info {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.login-info i {
    margin-left: var(--spacing-sm);
    color: var(--info-color);
}

/* ===== التطبيق الرئيسي ===== */
.main-app {
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar content";
    grid-template-columns: 280px 1fr;
    grid-template-rows: 70px 1fr;
    height: 100vh;
}

/* ===== شريط التنقل العلوي ===== */
.top-navbar {
    grid-area: header;
    background: var(--bg-card);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    z-index: 100;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.navbar-brand i {
    margin-left: var(--spacing-sm);
    font-size: 1.5rem;
}

.navbar-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.current-time {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.current-date {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.theme-toggle,
.logout-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--bg-primary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover,
.logout-btn:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== القائمة الجانبية ===== */
.sidebar {
    grid-area: sidebar;
    background: var(--bg-card);
    box-shadow: var(--shadow-light);
    overflow-y: auto;
    z-index: 50;
}

.sidebar-nav {
    padding: var(--spacing-lg) 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    margin-right: var(--spacing-md);
}

.nav-link:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
    transform: translateX(-4px);
}

.nav-link i {
    margin-left: var(--spacing-md);
    width: 20px;
    text-align: center;
}

.nav-item.active .nav-link {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-hover);
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    grid-area: content;
    padding: var(--spacing-lg);
    overflow-y: auto;
    background: var(--bg-primary);
}

/* ===== النوافذ المنبثقة ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    width: 90%;
    max-width: 500px;
    overflow: hidden;
}

.modal-header {
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--text-muted);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    background: var(--bg-primary);
}

/* ===== الأزرار ===== */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-secondary);
    box-shadow: var(--shadow-light);
}

.btn-success {
    background: var(--success-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-danger {
    background: var(--error-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn:active {
    transform: translateY(0);
}

/* ===== شريط التحميل ===== */
.loading-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--bg-primary);
    z-index: 1001;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    animation: loading 2s ease-in-out infinite;
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* ===== الإشعارات ===== */
.notifications {
    position: fixed;
    top: 80px;
    right: var(--spacing-lg);
    z-index: 1000;
    max-width: 400px;
}

.notification {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-right: 4px solid var(--success-color);
}

.notification.warning {
    border-right: 4px solid var(--warning-color);
}

.notification.error {
    border-right: 4px solid var(--error-color);
}

.notification.info {
    border-right: 4px solid var(--info-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== أنماط الصفحات ===== */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.page-header h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.page-header h1 i {
    color: var(--primary-color);
}

.page-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* ===== البطاقات ===== */
.card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--text-muted);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-body {
    color: var(--text-secondary);
}

/* ===== الجداول ===== */
.table-container {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--text-muted);
}

.table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background: var(--bg-primary);
}

/* ===== النماذج ===== */
.form-container {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-control {
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    box-shadow: var(--shadow-inset);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* ===== الإحصائيات ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 1.5rem;
    color: white;
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-icon.success {
    background: var(--success-color);
}

.stat-icon.warning {
    background: var(--warning-color);
}

.stat-icon.info {
    background: var(--info-color);
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* ===== شريط البحث ===== */
.search-bar {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-right: 3rem;
    border: none;
    border-radius: var(--border-radius);
    background: var(--bg-card);
    box-shadow: var(--shadow-light);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    color: var(--text-primary);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    box-shadow: var(--shadow-light), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
    .main-app {
        grid-template-areas:
            "header"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: 70px 1fr;
    }

    .sidebar {
        position: fixed;
        top: 70px;
        right: -280px;
        width: 280px;
        height: calc(100vh - 70px);
        transition: var(--transition);
        z-index: 200;
    }

    .sidebar.open {
        right: 0;
    }

    .navbar-center {
        display: none;
    }

    .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

/* ===== أنماط لوحة المعلومات ===== */
.dashboard-content {
    margin-top: var(--spacing-xl);
}

.row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

.col-md-8 {
    grid-column: 1;
}

.col-md-4 {
    grid-column: 1;
}

@media (min-width: 768px) {
    .row {
        grid-template-columns: 2fr 1fr;
    }

    .col-md-8 {
        grid-column: 1;
    }

    .col-md-4 {
        grid-column: 2;
    }
}

/* ===== الرسوم البيانية ===== */
.chart-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.simple-chart {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 150px;
    width: 100%;
    gap: 2px;
    margin-bottom: var(--spacing-md);
}

.chart-bar {
    background: linear-gradient(to top, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    min-height: 10px;
    flex: 1;
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.chart-bar:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}

.bar-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    white-space: nowrap;
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.no-data {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-xl);
}

.no-data i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* ===== التنبيهات ===== */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-success {
    background: rgba(72, 187, 120, 0.1);
    border-right: 4px solid var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(237, 137, 54, 0.1);
    border-right: 4px solid var(--warning-color);
    color: var(--warning-color);
}

.alert-danger {
    background: rgba(245, 101, 101, 0.1);
    border-right: 4px solid var(--error-color);
    color: var(--error-color);
}

.alert-info {
    background: rgba(66, 153, 225, 0.1);
    border-right: 4px solid var(--info-color);
    color: var(--info-color);
}

.alert-content {
    flex: 1;
}

/* ===== عناصر أفضل المنتجات ===== */
.top-product-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.top-product-item:hover {
    background: var(--bg-secondary);
    transform: translateX(-4px);
}

.product-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-left: var(--spacing-md);
}

.product-info {
    flex: 1;
}

/* ===== عناصر العملاء المدينين ===== */
.debtor-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.debtor-item:hover {
    background: var(--bg-secondary);
    transform: translateX(-4px);
}

.customer-info {
    flex: 1;
}

.debt-amount {
    font-weight: 600;
    color: var(--error-color);
}

/* ===== الشارات ===== */
.badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--error-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

.badge-secondary {
    background: var(--text-muted);
    color: white;
}

/* ===== أخطاء النماذج ===== */
.field-error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.form-control.error {
    border-color: var(--error-color);
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(245, 101, 101, 0.1);
}

/* ===== حالات التحميل ===== */
.page-loading,
.dashboard-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-primary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 480px) {
    .login-container {
        margin: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .table-container {
        overflow-x: auto;
    }

    .chart-labels span {
        font-size: 10px;
    }

    .simple-chart {
        height: 100px;
    }

    .top-product-item,
    .debtor-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
}

/* ===== أنماط صفحة المبيعات ===== */
.sales-container {
    margin-top: var(--spacing-lg);
}

.search-section {
    margin-bottom: var(--spacing-xl);
}

.search-results {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    max-height: 300px;
    overflow-y: auto;
    z-index: 100;
    display: none;
}

.search-result-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--text-muted);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.search-result-item:hover {
    background: var(--bg-primary);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item.no-results {
    text-align: center;
    color: var(--text-muted);
    cursor: default;
}

.search-result-item.no-results:hover {
    background: transparent;
}

.product-info {
    flex: 1;
}

.product-price {
    font-weight: 600;
    color: var(--primary-color);
}

/* ===== جدول المنتجات المضافة ===== */
.sale-items {
    margin-top: var(--spacing-xl);
}

.sale-items h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quantity-value {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
}

/* ===== ملخص البيع ===== */
.sale-summary {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--text-muted);
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.total {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    border-top: 2px solid var(--primary-color);
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-md);
}

/* ===== أنماط الفاتورة ===== */
.invoice {
    max-width: 400px;
    margin: 0 auto;
    font-family: 'Cairo', Arial, sans-serif;
    direction: rtl;
}

.invoice-header {
    text-align: center;
    margin-bottom: 20px;
}

.invoice-header h2 {
    margin: 0 0 10px 0;
    color: #333;
}

.invoice-header h3 {
    margin: 20px 0 10px 0;
    color: #666;
}

.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.invoice-table th,
.invoice-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: right;
}

.invoice-table th {
    background: #f5f5f5;
    font-weight: 600;
}

.invoice-summary {
    margin-top: 20px;
    text-align: right;
}

.invoice-summary p {
    margin: 5px 0;
}

.invoice-summary h3 {
    margin: 15px 0 5px 0;
    color: #333;
    border-top: 2px solid #333;
    padding-top: 10px;
}

.invoice-footer {
    text-align: center;
    margin-top: 30px;
    color: #666;
}

/* ===== النوافذ المنبثقة الكبيرة ===== */
.modal-content.large {
    max-width: 90vw;
    width: 1000px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--text-muted);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

/* ===== أزرار صغيرة ===== */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* ===== تحسينات متجاوبة للمبيعات ===== */
@media (max-width: 768px) {
    .sales-container .row {
        grid-template-columns: 1fr;
    }

    .search-results {
        position: fixed;
        top: auto;
        bottom: 0;
        right: 0;
        left: 0;
        max-height: 50vh;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .quantity-controls {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .modal-content.large {
        width: 95vw;
        max-width: none;
        margin: var(--spacing-md);
    }

    .modal-body {
        max-height: 50vh;
    }
}

@media (max-width: 480px) {
    .search-result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .sale-summary {
        font-size: var(--font-size-sm);
    }

    .summary-row.total {
        font-size: var(--font-size-base);
    }
}

/* ===== أنماط صفحة المنتجات ===== */
.filters-section {
    margin-bottom: var(--spacing-lg);
}

.product-image {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    border: 1px solid var(--text-muted);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image i {
    color: var(--text-muted);
    font-size: 1.5rem;
}

.quantity-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.quantity-badge.in-stock {
    background: rgba(72, 187, 120, 0.1);
    color: var(--success-color);
}

.quantity-badge.low-stock {
    background: rgba(237, 137, 54, 0.1);
    color: var(--warning-color);
}

.quantity-badge.out-of-stock {
    background: rgba(245, 101, 101, 0.1);
    color: var(--error-color);
}

.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

/* ===== نموذج المنتج ===== */
.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-control {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    border-right: none;
}

.image-preview {
    margin-top: var(--spacing-md);
    text-align: center;
}

.image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--text-muted);
}

/* ===== نافذة الاستيراد ===== */
.import-section {
    text-align: center;
    padding: var(--spacing-xl);
}

.import-section p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.import-section .form-control {
    margin-bottom: var(--spacing-lg);
}

.import-section .form-actions {
    justify-content: center;
    border-top: none;
    padding-top: 0;
}

/* ===== تحسينات متجاوبة للمنتجات ===== */
@media (max-width: 768px) {
    .filters-section .form-row {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .product-image {
        width: 40px;
        height: 40px;
    }

    .form-actions {
        flex-direction: column;
    }

    .input-group {
        flex-direction: column;
    }

    .input-group .form-control,
    .input-group .btn {
        border-radius: var(--border-radius);
    }
}

@media (max-width: 480px) {
    .table-container {
        font-size: var(--font-size-sm);
    }

    .quantity-badge {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs);
    }

    .action-buttons .btn {
        padding: var(--spacing-xs);
    }
}

/* ===== أنماط صفحة العملاء ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.primary {
    background: var(--primary-color);
}

.stat-icon.success {
    background: var(--success-color);
}

.stat-icon.warning {
    background: var(--warning-color);
}

.stat-icon.info {
    background: var(--info-color);
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.balance-amount {
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.balance-amount.debt {
    background: rgba(245, 101, 101, 0.1);
    color: var(--error-color);
}

.balance-amount.clear {
    background: rgba(72, 187, 120, 0.1);
    color: var(--success-color);
}

/* ===== نافذة تعديل الرصيد ===== */
.customer-info {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.customer-info h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.customer-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* ===== تحسينات متجاوبة للعملاء ===== */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .customer-info {
        padding: var(--spacing-md);
    }

    .balance-amount {
        font-size: var(--font-size-sm);
        padding: var(--spacing-xs);
    }
}

/* ===== أنماط صفحة الإعدادات ===== */
.tabs-container {
    margin-bottom: var(--spacing-xl);
}

.tabs {
    display: flex;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow-x: auto;
    margin-bottom: var(--spacing-lg);
}

.tab-button {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.tab-button:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.tab-button.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.radio-group {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-sm);
}

.radio-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-primary);
}

.radio-label input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.tax-rates-section {
    margin-top: var(--spacing-lg);
}

.tax-rate-item {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.input-group {
    display: flex;
    align-items: stretch;
}

.input-group-append {
    display: flex;
}

.input-group-text {
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    color: var(--text-secondary);
    font-weight: 600;
}

/* أزرار المبالغ السريعة */
.quick-amount-buttons {
    margin-top: var(--spacing-sm);
}

.quick-amount-buttons .btn-group {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.quick-amount-buttons .btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.quick-amount-buttons .btn-outline-secondary {
    border-color: var(--border-color);
    color: var(--text-secondary);
}

.quick-amount-buttons .btn-outline-secondary:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.quick-amount-buttons .btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
}

.quick-amount-buttons .btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* تفاصيل المدفوعات في جدول الديون */
.payment-breakdown {
    font-size: 0.9em;
    line-height: 1.4;
}

.payment-breakdown div {
    margin-bottom: 2px;
    color: #666;
}

.payment-breakdown strong {
    color: var(--text-color);
    font-size: 1em;
}

/* أنماط إدارة الباركود */
.barcode-preview {
    text-align: center;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

.barcode-manager-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-size: 14px;
    color: #666;
}

.tab-btn:hover {
    background: #f5f5f5;
    color: #333;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.barcode-result {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
}

.barcode-item {
    text-align: center;
    padding: 15px;
    margin: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    display: inline-block;
    vertical-align: top;
}

.barcode-item h4,
.barcode-item h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.barcode-canvas {
    margin: 10px 0;
}

.barcode-text {
    font-family: monospace;
    font-size: 12px;
    color: #666;
    margin: 5px 0;
}

.barcode-actions {
    margin-top: 10px;
}

.barcode-actions .btn {
    margin: 0 5px;
}

.products-selection {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background: white;
}

.product-selection-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.product-selection-item:last-child {
    border-bottom: none;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
}

.checkbox-label input[type="checkbox"] {
    margin-left: 10px;
    margin-right: 0;
}

.product-info {
    flex: 1;
}

.product-info strong {
    display: block;
    color: #333;
    margin-bottom: 2px;
}

.product-info small {
    color: #666;
    font-size: 12px;
}

.bulk-results-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #e8f5e8;
    border-radius: 8px;
}

.bulk-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.bulk-actions {
    text-align: center;
    padding: 15px;
    border-top: 1px solid #ddd;
}

.bulk-actions .btn {
    margin: 0 10px;
}

.print-preview {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    background: white;
    margin: 15px 0;
    min-height: 200px;
}

.print-options {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

/* تحسينات للنوافذ الكبيرة */
.modal-content.large {
    max-width: 900px;
    width: 90%;
}

/* أنماط الطباعة */
@media print {
    body * {
        visibility: hidden;
    }

    .print-area, .print-area * {
        visibility: visible;
    }

    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .barcode-label {
        border: 2px solid #000 !important;
        margin: 5mm !important;
        padding: 5mm !important;
        page-break-inside: avoid;
        background: white !important;
    }

    .product-name {
        font-size: 14pt !important;
        font-weight: bold !important;
        margin-bottom: 3mm !important;
    }

    .barcode-text {
        font-size: 10pt !important;
        font-family: 'Courier New', monospace !important;
        margin: 2mm 0 !important;
    }

    .product-price {
        font-size: 12pt !important;
        margin-top: 3mm !important;
    }

    canvas {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .barcode-manager-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: center;
        border-bottom: 1px solid #ddd;
        border-right: none;
    }

    .tab-btn.active {
        border-bottom: 1px solid var(--primary-color);
        border-right: none;
    }

    .bulk-results-grid {
        grid-template-columns: 1fr;
    }

    .modal-content.large {
        width: 95%;
        margin: 10px;
    }

    .barcode-item {
        width: 100%;
        margin: 5px 0;
    }
}

.logo-preview {
    margin-top: var(--spacing-md);
    text-align: center;
}

.logo-image {
    max-width: 200px;
    max-height: 150px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: var(--spacing-md);
}

/* ===== أنماط صفحة النسخ الاحتياطي ===== */
.quick-backup-section {
    margin-bottom: var(--spacing-xl);
}

.backup-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.backup-option {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    transition: var(--transition);
}

.backup-option:hover {
    background: var(--bg-secondary);
    transform: translateY(-2px);
}

.backup-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.backup-history-section {
    margin-bottom: var(--spacing-xl);
}

.backup-includes {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.backup-include-item {
    background: var(--bg-primary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.restore-options {
    margin-bottom: var(--spacing-lg);
}

.file-info {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.backup-file-info h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.backup-file-info p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.backup-file-info ul {
    margin: var(--spacing-sm) 0;
    padding-right: var(--spacing-lg);
    color: var(--text-secondary);
}

.progress-container {
    margin-bottom: var(--spacing-lg);
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.progress-details {
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* ===== أنماط صفحة التقارير ===== */
.reports-tabs {
    display: flex;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: var(--spacing-lg);
    overflow-x: auto;
}

.report-tab {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.report-tab:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.report-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.report-filters {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    align-items: end;
}

.date-presets {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.preset-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    border: 1px solid var(--text-muted);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.preset-btn:hover,
.preset-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.report-content {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.report-stat {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    text-align: center;
}

.report-stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.report-stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.chart-section {
    margin-bottom: var(--spacing-xl);
}

.chart-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.chart-wrapper {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.report-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
    flex-wrap: wrap;
}

/* ===== تحسينات متجاوبة للصفحات الجديدة ===== */
@media (max-width: 768px) {
    .tabs {
        flex-direction: column;
    }

    .tab-button {
        border-radius: 0;
        border-bottom: 1px solid var(--text-muted);
    }

    .tab-button:last-child {
        border-bottom: none;
    }

    .backup-options {
        grid-template-columns: 1fr;
    }

    .backup-actions {
        flex-direction: column;
    }

    .reports-tabs {
        flex-direction: column;
    }

    .report-tab {
        border-bottom: 1px solid var(--text-muted);
    }

    .report-tab:last-child {
        border-bottom: none;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .date-presets {
        justify-content: center;
    }

    .report-stats {
        grid-template-columns: 1fr;
    }

    .report-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .radio-group {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .backup-include-item {
        font-size: 10px;
        padding: 2px 6px;
    }

    .chart-wrapper {
        min-height: 300px;
        padding: var(--spacing-md);
    }

    .preset-btn {
        font-size: 10px;
        padding: var(--spacing-xs);
    }
}

/* ===== أنماط صفحة الخطأ ===== */
.error-page {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin: var(--spacing-xl) auto;
    max-width: 500px;
}

.error-page h2 {
    color: var(--error-color);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.error-page h2 i {
    font-size: 2rem;
}

.error-page p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== أنماط حالة التحميل المحسنة ===== */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    min-height: 300px;
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 4px solid var(--bg-primary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

.loading-text {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    font-weight: 500;
}

.loading-subtext {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-sm);
}

/* ===== أنماط الرسائل الفارغة ===== */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.empty-state p {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.empty-state .btn {
    margin-top: var(--spacing-md);
}

/* ===== أنماط إعدادات الفاتورة ===== */
.settings-section {
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.settings-section h4 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--bg-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.settings-section h4 i {
    font-size: var(--font-size-base);
}

/* أنماط الهوامش المخصصة */
.margin-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

.margin-inputs input {
    text-align: center;
}

/* أنماط معاينة الفاتورة */
.invoice-preview-container {
    background: white;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    box-shadow: var(--shadow-light);
}

.invoice-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--primary-color);
}

.invoice-preview-title {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.invoice-preview-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* أنماط نموذج إعدادات الفاتورة */
#invoiceSettingsForm .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

#invoiceSettingsForm .form-group {
    margin-bottom: var(--spacing-lg);
}

#invoiceSettingsForm .form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

#invoiceSettingsForm .checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color 0.3s ease;
}

#invoiceSettingsForm .checkbox-label:hover {
    background-color: var(--bg-primary);
}

#invoiceSettingsForm input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

#invoiceSettingsForm input[type="color"] {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
}

#invoiceSettingsForm textarea {
    resize: vertical;
    min-height: 80px;
}

/* أنماط خاصة للحجم المخصص */
#customSizeGroup {
    transition: all 0.3s ease;
}

#customSizeGroup .form-row {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

#customSizeGroup input {
    text-align: center;
}

/* أنماط معاينة الشعار */
.logo-preview {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-md);
    border: 2px dashed var(--bg-primary);
    border-radius: var(--border-radius-sm);
    text-align: center;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-preview img {
    max-width: 100%;
    max-height: 80px;
    object-fit: contain;
}

.logo-preview.empty {
    color: var(--text-muted);
    font-style: italic;
}

/* أنماط متجاوبة لإعدادات الفاتورة */
@media (max-width: 768px) {
    .settings-section {
        padding: var(--spacing-md);
    }

    #invoiceSettingsForm .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .margin-inputs {
        grid-template-columns: 1fr 1fr;
    }

    .invoice-preview-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .invoice-preview-actions {
        justify-content: center;
    }
}

/* أنماط الطباعة للفاتورة */
@media print {
    .invoice-container {
        box-shadow: none !important;
        border: none !important;
    }

    .invoice-preview-header,
    .invoice-preview-actions {
        display: none !important;
    }

    body {
        background: white !important;
    }
}

/* ===== الوضع الليلي ===== */
body.dark-mode {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-card: #2d3748;
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --shadow-light: 8px 8px 16px #0f1419, -8px -8px 16px #252f3f;
    --shadow-inset: inset 8px 8px 16px #0f1419, inset -8px -8px 16px #252f3f;
    --shadow-hover: 4px 4px 8px #0f1419, -4px -4px 8px #252f3f;
}

body.dark-mode {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

body.dark-mode .main-app {
    background-color: var(--bg-primary);
}

body.dark-mode .sidebar {
    background-color: var(--bg-secondary);
    border-right: 1px solid #4a5568;
}

body.dark-mode .sidebar-nav a {
    color: var(--text-secondary);
}

body.dark-mode .sidebar-nav a:hover,
body.dark-mode .sidebar-nav a.active {
    background-color: var(--primary-color);
    color: white;
}

body.dark-mode .main-content {
    background-color: var(--bg-primary);
}

body.dark-mode .stats-card,
body.dark-mode .card,
body.dark-mode .form-card {
    background-color: var(--bg-card);
    color: var(--text-primary);
    box-shadow: var(--shadow-light);
}

body.dark-mode .btn {
    background-color: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid #4a5568;
}

body.dark-mode .btn:hover {
    box-shadow: var(--shadow-hover);
}

body.dark-mode .btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
}

body.dark-mode .form-group input,
body.dark-mode .form-group select,
body.dark-mode .form-group textarea {
    background-color: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid #4a5568;
}

body.dark-mode .form-group input:focus,
body.dark-mode .form-group select:focus,
body.dark-mode .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

body.dark-mode .table {
    background-color: var(--bg-card);
    color: var(--text-primary);
}

body.dark-mode .table th {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-bottom: 1px solid #4a5568;
}

body.dark-mode .table td {
    border-bottom: 1px solid #4a5568;
}

body.dark-mode .modal-content {
    background-color: var(--bg-card);
    color: var(--text-primary);
}

body.dark-mode .page-header h1 {
    color: var(--text-primary);
}

/* ===== أنماط العملة - الجنيه المصري فقط ===== */
[data-currency="EGP"] .currency::after,
.currency::after {
    content: " ج.م";
}

/* ===== أنماط الأرقام ===== */
[data-number-type="arabic"] .number {
    font-family: 'Cairo', sans-serif;
}

[data-number-type="english"] .number {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== أنماط المنتجات المبنية على الكرتون ===== */
.product-name-info {
    line-height: 1.4;
}

.product-name-info .text-info {
    color: var(--info-color) !important;
    font-weight: 500;
}

.product-name-info .text-muted {
    color: var(--text-muted) !important;
}

.price-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
}

.price-info .price-main {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.price-info .price-secondary {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.price-info .price-carton {
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 600;
}

.price-info .price-profit {
    font-size: 0.75rem;
    color: var(--success-color);
    font-weight: 500;
}

.stock-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 160px;
}

.stock-info.carton-stock {
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: var(--border-radius-sm);
    padding: 8px;
}

.stock-info.regular-stock {
    text-align: center;
}

.stock-summary {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stock-main {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
}

.stock-units {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.stock-total {
    border-top: 1px solid rgba(102, 126, 234, 0.2);
    padding-top: 6px;
    margin-top: 4px;
}

.total-available {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.total-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.stock-breakdown {
    text-align: center;
    margin-top: 4px;
}

.stock-breakdown small {
    font-size: 0.7rem;
    line-height: 1.2;
}

.quantity-badge {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
}

.quantity-badge.main {
    font-size: 0.875rem;
    padding: 6px 12px;
}

.quantity-badge.total {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    font-weight: 700;
}

.quantity-badge.low-stock {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.quantity-badge.out-of-stock {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

/* أنماط نموذج المنتج المبني على الكرتون */
#cartonBasedFields .alert {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--info-color);
    background: rgba(66, 153, 225, 0.1);
    color: var(--info-color);
}

#cartonBasedFields .form-row {
    margin-bottom: 1rem;
}

#cartonBasedFields .form-group label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

#cartonBasedFields input[readonly] {
    background-color: var(--bg-primary);
    color: var(--text-secondary);
}

/* تحسين عرض الجدول للمنتجات المبنية على الكرتون */
.table td {
    vertical-align: middle;
}

.badge.badge-primary {
    background: var(--primary-color);
    color: white;
}

.badge.badge-secondary {
    background: var(--text-secondary);
    color: white;
}

/* تحسين عرض المعلومات في الجوال */
@media (max-width: 768px) {
    .stock-info {
        min-width: auto;
    }

    .stock-info.carton-stock {
        padding: 6px;
    }

    .stock-main, .stock-units {
        font-size: 0.75rem;
    }

    .price-info {
        min-width: auto;
    }

    .price-info .price-main,
    .price-info .price-carton {
        font-size: 0.75rem;
    }

    .price-info .price-secondary,
    .price-info .price-profit {
        font-size: 0.7rem;
    }

    .stock-breakdown small {
        font-size: 0.65rem;
    }

    .product-name-info small {
        font-size: 0.7rem;
    }
}

/* تحسين عرض الجدول */
.table td {
    vertical-align: middle;
    padding: 12px 8px;
}

.table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
}

/* ===== تنسيقات جدول المنتجات المتجاوب ===== */
.products-table-container {
    width: 100%;
    overflow: hidden;
}

/* عرض الجدول للشاشات الكبيرة */
.desktop-view {
    display: block;
}

.medium-view {
    display: none;
}

.mobile-view {
    display: none;
}

/* تنسيق أعمدة الجدول */
.products-table {
    min-width: 1200px;
    table-layout: fixed;
}

.products-table .col-image {
    width: 80px;
}

.products-table .col-name {
    width: 200px;
}

.products-table .col-barcode {
    width: 120px;
}

.products-table .col-category {
    width: 100px;
}

.products-table .col-type {
    width: 80px;
}

.products-table .col-price {
    width: 150px;
}

.products-table .col-stock {
    width: 180px;
}

.products-table .col-status {
    width: 100px;
}

.products-table .col-actions {
    width: 180px;
}

/* تنسيق الجدول المبسط للشاشات المتوسطة */
.products-table-medium {
    width: 100%;
}

.products-table-medium .col-product {
    width: 40%;
}

.products-table-medium .col-price {
    width: 20%;
}

.products-table-medium .col-stock {
    width: 20%;
}

.products-table-medium .col-actions {
    width: 20%;
}

/* تنسيق بطاقات المنتجات للشاشات الصغيرة */
.product-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-light);
}

.product-card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.product-card-image {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-left: var(--spacing-sm);
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-card-info {
    flex: 1;
}

.product-card-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.product-card-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.product-card-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
}

.product-card-actions {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 1200px) {
    .desktop-view {
        display: none;
    }

    .medium-view {
        display: block;
    }

    .mobile-view {
        display: none;
    }
}

@media (max-width: 768px) {
    .desktop-view {
        display: none;
    }

    .medium-view {
        display: none;
    }

    .mobile-view {
        display: block;
    }

    .product-card-details {
        grid-template-columns: 1fr;
    }

    .product-card-actions .btn {
        padding: var(--spacing-xs);
        font-size: var(--font-size-sm);
    }
}

/* تنسيقات إضافية للجدول المبسط */
.product-summary {
    display: flex;
    align-items: center;
}

.product-main-info {
    display: flex;
    align-items: center;
    width: 100%;
}

.product-image-small {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-left: var(--spacing-sm);
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.product-image-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image-small i {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.product-details {
    flex: 1;
}

.price-compact {
    text-align: center;
}

.stock-compact {
    text-align: center;
}

.stock-breakdown {
    margin-top: 4px;
}

.action-buttons-compact {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons-compact .btn {
    padding: var(--spacing-xs);
    min-width: 32px;
}

/* تحسين عرض الكميات */
.quantity-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.quantity-badge.in-stock {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.quantity-badge.low-stock {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.quantity-badge.out-of-stock {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* تحسين عرض الأزرار في البطاقات */
.product-card-actions .btn {
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تحسين الاستجابة للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .product-card-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .product-card-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .action-buttons-compact {
        flex-direction: column;
    }

    .action-buttons-compact .btn {
        width: 100%;
        justify-content: center;
    }
}

/* تحسين عرض الجدول الرئيسي */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.products-table {
    white-space: nowrap;
}

.products-table td,
.products-table th {
    padding: 8px 6px;
    font-size: var(--font-size-sm);
}

/* تحسين عرض معلومات المنتج في الجدول */
.product-name-info {
    max-width: 200px;
    white-space: normal;
    word-wrap: break-word;
}

.price-info {
    text-align: center;
    min-width: 120px;
}

.price-main {
    font-weight: 600;
    color: var(--primary-color);
}

.price-secondary {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.price-carton {
    color: var(--text-primary);
    font-weight: 600;
}

.price-profit {
    color: var(--success-color);
    font-size: var(--font-size-xs);
}

.stock-info {
    text-align: center;
    min-width: 150px;
}

.carton-stock .stock-summary {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;
}

.stock-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.stock-units {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: var(--font-size-sm);
}

.stock-total {
    border-top: 1px solid var(--border-color);
    padding-top: 8px;
}

.total-available {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 4px;
}

.total-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.quantity-badge.total {
    font-weight: 700;
    padding: 4px 8px;
}

.stock-breakdown {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.regular-stock {
    display: flex;
    justify-content: center;
}

.quantity-badge.main {
    font-size: var(--font-size-base);
    padding: 6px 12px;
}

/* تحسين الأزرار */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    padding: 4px 6px;
    min-width: 32px;
}

/* تحسين الألوان للوضع الليلي */
body.dark-mode .quantity-badge.in-stock {
    background: #1e4d2b;
    color: #4ade80;
    border-color: #166534;
}

body.dark-mode .quantity-badge.low-stock {
    background: #451a03;
    color: #fbbf24;
    border-color: #92400e;
}

body.dark-mode .quantity-badge.out-of-stock {
    background: #4c1d1d;
    color: #f87171;
    border-color: #991b1b;
}

body.dark-mode .product-card {
    background: var(--bg-secondary);
    border-color: #4a5568;
}

body.dark-mode .product-card-detail {
    background: var(--bg-primary);
}

/* ===== تنسيقات ضبط المصنع ===== */
.factory-reset-info {
    margin: var(--spacing-lg) 0;
}

.factory-reset-info h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.reset-items-list {
    list-style: none;
    padding: 0;
    margin: var(--spacing-md) 0;
}

.reset-items-list li {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    border-left: 4px solid #dc3545;
}

.reset-items-list li i {
    margin-left: var(--spacing-sm);
    width: 20px;
    text-align: center;
}

.factory-reset-options {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.factory-reset-options .form-group {
    margin-bottom: var(--spacing-lg);
}

.factory-reset-options .checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.factory-reset-options .form-text {
    margin-top: var(--spacing-xs);
    margin-right: 24px;
}

#factoryResetConfirmation {
    border: 2px solid #dc3545;
    background: #fff5f5;
}

#factoryResetConfirmation:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

#factoryResetButton:disabled {
    background: var(--text-muted) !important;
    border-color: var(--text-muted) !important;
    color: white !important;
    cursor: not-allowed !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* تحسين التنبيهات */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.alert-danger {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.alert i {
    margin-top: 2px;
    flex-shrink: 0;
}

/* الوضع الليلي لضبط المصنع */
body.dark-mode .reset-items-list li {
    background: var(--bg-secondary);
    border-left-color: #dc3545;
}

body.dark-mode #factoryResetConfirmation {
    background: var(--bg-secondary);
    border-color: #dc3545;
    color: var(--text-primary);
}

body.dark-mode .alert-danger {
    background: #4c1d1d;
    border-color: #991b1b;
    color: #f87171;
}

body.dark-mode .alert-info {
    background: #1e3a5f;
    border-color: #2563eb;
    color: #60a5fa;
}

/* أيقونات ملونة */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}
